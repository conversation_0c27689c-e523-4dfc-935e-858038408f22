# Xpanel Tip Amounts CRUD API

This document describes the CRUD API for managing tip amounts through xpanel with OID support.

## Overview

The xpanel tip amounts API allows administrators to manage tip amount configurations that are used by the client applications. Each tip amount has a unique OID (Object Identifier) for identification and supports both static amounts and percentage-based tips.

## Database Schema

The API uses a new table `order_tip_amounts_xpanel` with the following structure:

```sql
CREATE TABLE `order_tip_amounts_xpanel` (
  `oid` VARCHAR(36) NOT NULL PRIMARY KEY,
  `amount` INT NOT NULL,
  `type` ENUM('static', 'percentage') NOT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_amount_type` (`amount`, `type`)
);
```

## API Endpoints

### 1. Get All Tip Amounts

**GET** `/v1/xpanel/tip-amounts`

Returns a list of all tip amounts.

**Response:**
```json
{
  "tip_amounts": [
    {
      "oid": "123e4567-e89b-12d3-a456-426614174000",
      "amount": 1000,
      "type": "static",
      "is_active": true,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1
}
```

### 2. Get Tip Amount by OID

**GET** `/v1/xpanel/tip-amounts/{oid}`

Returns a specific tip amount by its OID.

**Response:**
```json
{
  "oid": "123e4567-e89b-12d3-a456-426614174000",
  "amount": 1000,
  "type": "static",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### 3. Create Tip Amount

**POST** `/v1/xpanel/tip-amounts`

Creates a new tip amount.

**Request Body:**
```json
{
  "amount": 1000,
  "type": "static",
  "is_active": true
}
```

**Response:** (201 Created)
```json
{
  "oid": "123e4567-e89b-12d3-a456-426614174000",
  "amount": 1000,
  "type": "static",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### 4. Update Tip Amount

**PUT** `/v1/xpanel/tip-amounts/{oid}`

Updates an existing tip amount.

**Request Body:**
```json
{
  "amount": 1500,
  "type": "static",
  "is_active": false
}
```

**Response:**
```json
{
  "oid": "123e4567-e89b-12d3-a456-426614174000",
  "amount": 1500,
  "type": "static",
  "is_active": false,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:30:00Z"
}
```

### 5. Delete Tip Amount

**DELETE** `/v1/xpanel/tip-amounts/{oid}`

Deletes a tip amount by its OID.

**Response:** (204 No Content)

## Validation Rules

1. **Amount**: Must be greater than 0
2. **Type**: Must be either "static" or "percentage"
3. **Static amounts**: Must be at least 1000 UZS
4. **Percentage amounts**: Cannot exceed 100%
5. **Uniqueness**: Each combination of amount and type must be unique

## Error Responses

The API returns standardized error responses:

```json
{
  "error": {
    "type": "error_type",
    "message": "Error description"
  }
}
```

### Error Types

- `bad_request` (400): Invalid request data
- `data_validation` (400): Validation error
- `not_found` (404): Resource not found
- `duplicate_entry` (409): Duplicate data
- `service_error` (500): Internal server error

## Usage Examples

### Create a static tip amount
```bash
curl -X POST http://localhost:8000/v1/xpanel/tip-amounts \
  -H "Content-Type: application/json" \
  -d '{"amount": 2000, "type": "static", "is_active": true}'
```

### Create a percentage tip amount
```bash
curl -X POST http://localhost:8000/v1/xpanel/tip-amounts \
  -H "Content-Type: application/json" \
  -d '{"amount": 15, "type": "percentage", "is_active": true}'
```

### Get all tip amounts
```bash
curl http://localhost:8000/v1/xpanel/tip-amounts
```

### Update a tip amount
```bash
curl -X PUT http://localhost:8000/v1/xpanel/tip-amounts/{oid} \
  -H "Content-Type: application/json" \
  -d '{"amount": 2500, "type": "static", "is_active": false}'
```

### Delete a tip amount
```bash
curl -X DELETE http://localhost:8000/v1/xpanel/tip-amounts/{oid}
```

## Migration

To set up the database table, run the migration script:

```bash
mysql -u username -p database_name < migration_xpanel_tip_amounts.sql
```
