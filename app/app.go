package app

import (
	"context"
	"sync"
	"time"

	"info_service/model"
	"info_service/repo"
	"info_service/util/config"
	"info_service/util/logger"

	"github.com/maypok86/otter"
)

var (
	app  *App
	once sync.Once
)

type App struct {
	ctx  context.Context
	cfg  *config.Config
	log  *logger.Logger
	repo *repo.Repo
	// redis *redis.Client
	appReleasesInfoCache otter.Cache[string, model.AppUpdateResponse]
}

func Get() *App {
	once.Do(func() {
		cfg := config.Get()

		app = &App{
			ctx:  context.Background(),
			cfg:  cfg,
			log:  logger.Get(cfg.LogLevel),
			repo: repo.Get(),
			// redis: rds.Get(),
		}

		var err error
		app.appReleasesInfoCache, err = otter.MustBuilder[string, model.AppUpdateResponse](1000).WithTTL(5 * time.Minute).Build()
		if err != nil {
			panic(err)
		}
	})

	return app
}
