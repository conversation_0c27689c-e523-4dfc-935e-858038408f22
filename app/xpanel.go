package app

import (
	"context"
	"fmt"

	"info_service/model"
)

func (a *App) GetAllTipAmounts(ctx context.Context) (model.TipAmountListResponse, error) {
	tipAmounts, err := a.repo.GetAllTipAmounts(ctx)
	if err != nil {
		return model.TipAmountListResponse{}, err
	}

	total, err := a.repo.GetTipAmountsCount(ctx)
	if err != nil {
		return model.TipAmountListResponse{}, err
	}

	responses := make([]model.TipAmountResponse, len(tipAmounts))
	for i, tipAmount := range tipAmounts {
		responses[i] = tipAmount.ToResponse()
	}

	return model.TipAmountListResponse{
		TipAmounts: responses,
		Total:      total,
	}, nil
}

// GetTipAmountByOID retrieves a specific tip amount by OID
func (a *App) GetTipAmountByOID(ctx context.Context, Id string) (model.TipAmountResponse, error) {
	if Id == "" {
		return model.TipAmountResponse{}, fmt.<PERSON><PERSON><PERSON>("Id is required")
	}

	tipAmount, err := a.repo.GetTipAmountByOID(ctx, Id)
	if err != nil {
		return model.TipAmountResponse{}, err
	}

	return tipAmount.ToResponse(), nil
}

// CreateTipAmount creates a new tip amount
func (a *App) CreateTipAmount(ctx context.Context, req model.TipAmountRequest) (model.TipAmountResponse, error) {
	// Validate request
	if err := a.validateTipAmountRequest(req); err != nil {
		return model.TipAmountResponse{}, err
	}

	// Set default value for IsActive if not provided
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	// Create new tip amount
	tipAmount := model.NewTipAmount(req.Amount, req.Type, isActive)

	// Save to database
	err := a.repo.CreateTipAmount(ctx, tipAmount)
	if err != nil {
		return model.TipAmountResponse{}, err
	}

	return tipAmount.ToResponse(), nil
}

// UpdateTipAmount updates an existing tip amount
func (a *App) UpdateTipAmount(ctx context.Context, oid string, req model.TipAmountRequest) (model.TipAmountResponse, error) {
	if oid == "" {
		return model.TipAmountResponse{}, fmt.Errorf("OID is required")
	}

	// Validate request
	if err := a.validateTipAmountRequest(req); err != nil {
		return model.TipAmountResponse{}, err
	}

	// Get existing tip amount
	existingTipAmount, err := a.repo.GetTipAmountByOID(ctx, oid)
	if err != nil {
		return model.TipAmountResponse{}, err
	}

	// Set default value for IsActive if not provided
	isActive := existingTipAmount.IsActive
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	// Update tip amount
	existingTipAmount.Update(req.Amount, req.Type, isActive)

	// Save to database
	err = a.repo.UpdateTipAmount(ctx, oid, &existingTipAmount)
	if err != nil {
		return model.TipAmountResponse{}, err
	}

	return existingTipAmount.ToResponse(), nil
}

// DeleteTipAmount deletes a tip amount by OID
func (a *App) DeleteTipAmount(ctx context.Context, oid string) error {
	if oid == "" {
		return fmt.Errorf("OID is required")
	}

	return a.repo.DeleteTipAmount(ctx, oid)
}

// validateTipAmountRequest validates the tip amount request
func (a *App) validateTipAmountRequest(req model.TipAmountRequest) error {
	if req.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	if req.Type != "static" && req.Type != "percentage" {
		return fmt.Errorf("type must be either 'static' or 'percentage'")
	}

	// Additional validation for percentage type
	if req.Type == "percentage" && req.Amount > 100 {
		return fmt.Errorf("percentage amount cannot be greater than 100")
	}

	// Additional validation for static type (minimum 1000 UZS as per error messages)
	if req.Type == "static" && req.Amount < 1000 {
		return fmt.Errorf("static tip amount cannot be less than 1000 UZS")
	}

	return nil
}
