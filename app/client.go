package app

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"

	"info_service/model"
	"info_service/util"

	"github.com/guregu/null/v6"
)

func (a *App) GetClientAppUpdateInfo(ctx context.Context, version, platform string) (resp model.AppUpdateResponse, err error) {
	var (
		platformId int8
		updateUrl  string
		minVersion string
	)

	if version == "" || platform == "" {
		return
	}

	key := "client_app_" + platform + "_version:" + version

	resp, ok := a.appReleasesInfoCache.Get(key)
	if ok {
		return
	}

	switch platform {
	case "android":
		minVersion = a.repo.GetSysParam("minimal_client_android_app_version", "1.0.0").Str()
		platformId = 1
		updateUrl = a.cfg.ClientAndroidAppUpdateUrl
	case "ios", "iphone":
		minVersion = a.repo.GetSysParam("minimal_client_ios_app_version", "1.0.0").Str()
		platformId = 2
		updateUrl = a.cfg.ClientIosAppUpdateUrl
	default:
		return
	}

	if util.CompareVersion(version, minVersion) < 0 {
		resp.ForceUpdate = null.BoolFrom(true)
		resp.Url = updateUrl
		a.appReleasesInfoCache.Set(key, resp)
		return
	}

	info, err := a.repo.GetClientAppReleaseInfo(ctx, platformId, version)
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
		} else {
			a.log.Errorf("get client app %s release info: %v", version, err)
			err = nil
			return
		}
	}

	if info.Id == 0 {
		err = a.repo.CreateClientAppRelease(ctx, version, platformId)
		if err != nil {
			a.log.Errorf("create client app %s - %s release info: %v", platformId, version, err)
			err = nil
		}
		return
	}

	if info.IsBlocked {
		resp.ForceUpdate = null.BoolFrom(true)
		resp.Url = updateUrl
	} else if info.IsDeprecated {
		resp.ForceUpdate = null.BoolFrom(false)
		resp.Url = updateUrl
	}

	a.appReleasesInfoCache.Set(key, resp)

	return
}

func (a *App) GetClientTipAmounts(ctx context.Context, totalCost string) ([]int, error) {
	if totalCost != "" {
		percentages, err := a.repo.GetPercentageTipAmounts(ctx)
		if err != nil {
			return nil, err
		}

		totalCostInt, err := strconv.Atoi(totalCost)
		if err != nil {
			return nil, fmt.Errorf("invalid total_cost parameter")
		}

		tips := make([]int, len(percentages))
		for i, percentage := range percentages {
			rawTip := (totalCostInt * percentage) / 100

			if rawTip%500 != 0 {
				rawTip = ((rawTip + 499) / 500) * 500
			}

			if rawTip < 1000 {
				rawTip = 1000
				tips = make([]int, 1)
				tips[0] = rawTip
				return tips, nil
			}
			tips[i] = rawTip
		}

		return tips, nil
	}

	return a.repo.GetClientTipAmounts(ctx)
}
