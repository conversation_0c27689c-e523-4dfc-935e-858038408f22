package app

import (
	"context"
	"database/sql"
	"info_service/model"
	"info_service/util"

	"github.com/guregu/null/v6"
)

func (a *App) GetDriverAppUpdateInfo(ctx context.Context, version string) (resp model.AppUpdateResponse, err error) {
	if version == "" {
		return
	}

	key := "driver_app_version:" + version

	resp, ok := a.appReleasesInfoCache.Get(key)
	if ok {
		return
	}

	minVersion := a.repo.GetSysParam("minimal_driver_app_version", "1.0.0").Str()

	if util.CompareVersion(version, minVersion) < 0 {
		resp.ForceUpdate = null.BoolFrom(true)
		resp.Url = a.cfg.DriverAndroidAppUpdateUrl
		a.appReleasesInfoCache.Set(key, resp)
		return
	}

	info, err := a.repo.GetDriverAppReleaseInfo(ctx, version)
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
		} else {
			a.log.Errorf("get driver app %s release info: %v", version, err)
			err = nil
			return
		}
	}

	if info.Id == 0 {
		err = a.repo.CreateDriverAppRelease(ctx, version)
		if err != nil {
			a.log.Errorf("create driver app %s release info: %v", version, err)
			err = nil
		}
		return
	}

	if info.IsBlocked {
		resp.ForceUpdate = null.BoolFrom(true)
		resp.Url = a.cfg.DriverAndroidAppUpdateUrl
	} else if info.IsDeprecated {
		resp.ForceUpdate = null.BoolFrom(false)
		resp.Url = a.cfg.DriverAndroidAppUpdateUrl
	}

	a.appReleasesInfoCache.Set(key, resp)

	return
}
