package api

import (
	"net/http"

	"info_service/model"

	"github.com/gofiber/fiber/v3"
)

// getAllTipAmounts handles GET /v1/xpanel/tip-amounts
func (h *handler) getAllTipAmounts(c fiber.Ctx) error {
	response, err := h.app.GetAllTipAmounts(c.Context())
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(response)
}

// getTipAmountByOID handles GET /v1/xpanel/tip-amounts/:oid
func (h *handler) getTipAmountByOID(c fiber.Ctx) error {
	oid := c.Params("oid")
	if oid == "" {
		return c.Status(http.StatusBadRequest).JSON(errResponse{
			Body: errBody{Type: "bad_request", Message: "OID parameter is required"},
		})
	}

	response, err := h.app.GetTipAmountByOID(c.Context(), oid)
	if err != nil {
		if err.Error() == "tip amount with OID "+oid+" not found" {
			return c.Status(http.StatusNotFound).JSON(errResponse{
				Body: errBody{Type: "not_found", Message: err.Error()},
			})
		}
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(response)
}

// createTipAmount handles POST /v1/xpanel/tip-amounts
func (h *handler) createTipAmount(c fiber.Ctx) error {
	var req model.TipAmountRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(http.StatusBadRequest).JSON(errResponse{
			Body: errBody{Type: "bad_request", Message: "Invalid JSON payload"},
		})
	}

	response, err := h.app.CreateTipAmount(c.Context(), req)
	if err != nil {
		// Check if it's a validation error
		if err.Error() == "amount must be greater than 0" ||
			err.Error() == "type must be either 'static' or 'percentage'" ||
			err.Error() == "percentage amount cannot be greater than 100" ||
			err.Error() == "static tip amount cannot be less than 1000 UZS" {
			return c.Status(http.StatusBadRequest).JSON(errResponse{
				Body: errBody{Type: "data_validation", Message: err.Error()},
			})
		}
		// Check if it's a duplicate error
		if err.Error() != "" && err.Error()[:len("tip amount with amount")] == "tip amount with amount" {
			return c.Status(http.StatusConflict).JSON(errResponse{
				Body: errBody{Type: "duplicate_entry", Message: err.Error()},
			})
		}
		return serviceErrorResponse(c, err.Error())
	}

	return c.Status(http.StatusCreated).JSON(response)
}

// updateTipAmount handles PUT /v1/xpanel/tip-amounts/:oid
func (h *handler) updateTipAmount(c fiber.Ctx) error {
	oid := c.Params("oid")
	if oid == "" {
		return c.Status(http.StatusBadRequest).JSON(errResponse{
			Body: errBody{Type: "bad_request", Message: "OID parameter is required"},
		})
	}

	var req model.TipAmountRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(http.StatusBadRequest).JSON(errResponse{
			Body: errBody{Type: "bad_request", Message: "Invalid JSON payload"},
		})
	}

	response, err := h.app.UpdateTipAmount(c.Context(), oid, req)
	if err != nil {
		// Check if it's a not found error
		if err.Error() == "tip amount with OID "+oid+" not found" {
			return c.Status(http.StatusNotFound).JSON(errResponse{
				Body: errBody{Type: "not_found", Message: err.Error()},
			})
		}
		// Check if it's a validation error
		if err.Error() == "amount must be greater than 0" ||
			err.Error() == "type must be either 'static' or 'percentage'" ||
			err.Error() == "percentage amount cannot be greater than 100" ||
			err.Error() == "static tip amount cannot be less than 1000 UZS" {
			return c.Status(http.StatusBadRequest).JSON(errResponse{
				Body: errBody{Type: "data_validation", Message: err.Error()},
			})
		}
		// Check if it's a duplicate error
		if err.Error() != "" && len(err.Error()) > len("tip amount with amount") && err.Error()[:len("tip amount with amount")] == "tip amount with amount" {
			return c.Status(http.StatusConflict).JSON(errResponse{
				Body: errBody{Type: "duplicate_entry", Message: err.Error()},
			})
		}
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(response)
}

// deleteTipAmount handles DELETE /v1/xpanel/tip-amounts/:oid
func (h *handler) deleteTipAmount(c fiber.Ctx) error {
	oid := c.Params("oid")
	if oid == "" {
		return c.Status(http.StatusBadRequest).JSON(errResponse{
			Body: errBody{Type: "bad_request", Message: "OID parameter is required"},
		})
	}

	err := h.app.DeleteTipAmount(c.Context(), oid)
	if err != nil {
		if err.Error() == "tip amount with OID "+oid+" not found" {
			return c.Status(http.StatusNotFound).JSON(errResponse{
				Body: errBody{Type: "not_found", Message: err.Error()},
			})
		}
		return serviceErrorResponse(c, err.Error())
	}

	return c.Status(http.StatusNoContent).Send(nil)
}
