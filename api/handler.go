package api

import (
	"info_service/util"

	"github.com/gofiber/fiber/v3"
)

func (h *handler) getClientNewsList(c fiber.Ctx) error {
	// date := c.Query("date")
	// if date == "" {
	// 	return h.badRequestResponse(c, "incorrect date")
	// }

	// page := util.ParseInt(c.Query("page"))
	// if page == 0 {
	// 	page = 1
	// }
	// limit := util.ParseInt(c.Query("limit"))
	// if limit == 0 {
	// 	limit = 1
	// }

	// resp, err := h.repo.GetClientNews(c.Context(), date, util.GetLang(string(c.Request().Header.Peek("APP-LANGUAGE"))), page, limit)
	// if err != nil {
	// 	return serviceErrorResponse(c, err.Error())
	// }

	return c.JSON(fiber.Map{"news": []int{}})
}

func (h *handler) getClientNewsDetail(c fiber.Ctx) error {
	// newsId := util.ParseInt(c.Params("news_id"))
	// if newsId == 0 {
	// 	return h.badRequestResponse(c, "incorrect news_id")
	// }

	// resp, err := h.repo.GetNewsDetails(newsId)
	// if err != nil {
	// 	return serviceErrorResponse(c, err.Error())
	// }

	return c.JSON(fiber.Map{"info": nil})
}

func (h *handler) getClientTipAmounts(c fiber.Ctx) error {
	tips, err := h.app.GetClientTipAmounts(c.Context(), c.Query("total_cost"))
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{"tips": tips})
}

func (h *handler) getTermsAndConditions(c fiber.Ctx) error {
	resp, err := h.repo.GetTermsAndConditions(c.Context(), util.GetLang(string(c.Request().Header.Peek("App-Language"))))
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	c.Set("Content-Type", "text/html; charset=utf-8")

	return c.SendString(resp)
}

func (h *handler) getSystemSettings(c fiber.Ctx) error {
	return c.JSON(fiber.Map{"value": "0"})
}

func (h *handler) getClientAppUpdateInfo(c fiber.Ctx) error {
	result, err := h.app.GetClientAppUpdateInfo(c.Context(), string(c.Request().Header.Peek(("APP-VERSION"))), string(c.Request().Header.Peek(("SYSTEM"))))
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(result)
}

func (h *handler) getDriverAppUpdateInfo(c fiber.Ctx) error {
	result, err := h.app.GetDriverAppUpdateInfo(c.Context(), string(c.Request().Header.Peek(("APP-VERSION"))))
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(result)
}

func (h *handler) getOrderCancelReasons(c fiber.Ctx) error {
	resp, err := h.repo.GetOrderCancelReasons(c.Context(), util.GetLang(string(c.Request().Header.Peek("APP-LANGUAGE"))))
	if err != nil {
		return serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{"list": resp})
}
