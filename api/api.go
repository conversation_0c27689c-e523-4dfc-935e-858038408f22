package api

import (
	"context"
	"embed"
	"fmt"
	"net/http"
	"os"
	"time"

	"info_service/app"
	"info_service/repo"
	"info_service/util/config"
	"info_service/util/logger"

	json "github.com/bytedance/sonic"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/static"
)

//go:embed assets/error_messages.json
var assets embed.FS

type handler struct {
	ctx  context.Context
	cfg  *config.Config
	log  *logger.Logger
	repo *repo.Repo
	app  *app.App
}

func Run(ctx context.Context) {
	cfg := config.Get()

	h := &handler{
		ctx:  ctx,
		cfg:  cfg,
		log:  logger.Get(cfg.LogLevel),
		repo: repo.Get(),
		app:  app.Get(),
	}

	h.log.Info("running http server at: ", h.cfg.Host)

	api := h.newApi()

	if err := api.Listen(h.cfg.Host, fiber.ListenConfig{GracefulContext: ctx}); err != nil {
		panic(fmt.Errorf("cannot run http server: %v", err))
	}

	h.log.Info("shutting down, server exiting")
}

func (h *handler) newApi() *fiber.App {
	api := fiber.New(fiber.Config{
		ErrorHandler: func(c fiber.Ctx, err error) error {
			return c.Status(fiber.StatusInternalServerError).JSON(errResponse{Body: errBody{Type: "service_error", Message: err.Error()}})
		},
		JSONEncoder: json.Marshal,
		JSONDecoder: json.Unmarshal,
	})

	if h.cfg.RequestLogEnabled {
		api.Use(h.fiberLogger)
	} else {
		api.Use(h.fiberErrorLogger)
	}

	api.Get("/health", func(c fiber.Ctx) error { return c.SendStatus(http.StatusOK) })

	api.Get("/v1/error-messages", static.New("assets/error_messages.json", static.Config{FS: assets, MaxAge: 600, CacheDuration: 10 * time.Minute}))
	api.Get("/v1/terms-and-conditions", h.getTermsAndConditions)
	api.Get("/v1/clients/:client_id/news", h.getClientNewsList)
	api.Get("/v1/clients/:client_id/news/:news_id", h.getClientNewsDetail)
	api.Get("/v1/clients/:client_id/update-info", h.getClientAppUpdateInfo)
	api.Get("/v1/drivers/:driver_id/update-info", h.getDriverAppUpdateInfo)
	api.Get("/v1/clients/:client_id/tip-amounts", h.getClientTipAmounts)
	api.Get("/v1/clients/:client_id/order-cancel-reasons", h.getOrderCancelReasons)
	api.Get("/v1/system-settings", h.getSystemSettings) // TODO deprecated

	api.Use(func(c fiber.Ctx) error {
		return c.Status(fiber.StatusNotFound).SendString("API not found!")
	})

	return api
}

type errResponse struct {
	Body errBody `json:"error"`
}

type errBody struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

// func errorResponse(c fiber.Ctx, errType, msg string) error {
// 	return c.Status(http.StatusBadRequest).JSON(errResponse{Body: errBody{Type: errType, Message: msg}})
// }

// func badRequestResponse(c fiber.Ctx, msg string) error {
// 	return c.Status(http.StatusBadRequest).JSON(errResponse{Body: errBody{Type: "bad_request", Message: msg}})
// }

func serviceErrorResponse(c fiber.Ctx, msg string) error {
	return c.Status(http.StatusInternalServerError).JSON(errResponse{Body: errBody{Type: "service_error", Message: msg}})
}

const requestTimeout = 10 * time.Second

func (h *handler) fiberLogger(c fiber.Ctx) (err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			if err, ok = r.(error); !ok {
				err = fmt.Errorf("%v", r)
			}
		}
	}()

	start := time.Now()

	ctx, cancel := context.WithDeadline(h.ctx, start.Add(requestTimeout))
	defer cancel()

	c.SetContext(ctx)

	err = c.Next()

	req := c.Request()
	path := string(req.URI().PathOriginal())
	query := string(req.URI().QueryString())

	latency := time.Since(start)

	if query != "" {
		path += "?" + query
	}

	resp := c.Response()
	code := resp.StatusCode()

	if code != fiber.StatusOK {
		path += "    " + string(resp.Body())
	}

	msg := fmt.Sprintf("[FIBER]  | %3d | %13v | %-7s %s\n",
		code,
		latency,
		c.Method(),
		path,
	)

	fmt.Fprint(os.Stdout, msg)

	return
}

func (h *handler) fiberErrorLogger(c fiber.Ctx) (err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			if err, ok = r.(error); !ok {
				err = fmt.Errorf("%v", r)
			}
		}
	}()

	start := time.Now()

	ctx, cancel := context.WithDeadline(h.ctx, start.Add(requestTimeout))
	defer cancel()

	c.SetContext(ctx)

	err = c.Next()

	resp := c.Response()
	code := resp.StatusCode()

	if code != fiber.StatusOK {
		req := c.Request()
		path := string(req.URI().PathOriginal())
		query := string(req.URI().QueryString())

		latency := time.Since(start)

		if query != "" {
			path += "?" + query
		}

		path += "    " + string(resp.Body())

		msg := fmt.Sprintf("[FIBER]  | %3d | %13v | %-7s %s\n",
			code,
			latency,
			c.Method(),
			path,
		)

		fmt.Fprint(os.Stdout, msg)
	}

	return
}
