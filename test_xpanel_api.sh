#!/bin/bash

# Test script for xpanel tip amounts CRUD API
# Make sure the server is running before executing this script

BASE_URL="http://localhost:8000"
API_PATH="/v1/xpanel/tip-amounts"

echo "Testing xpanel tip amounts CRUD API..."
echo "======================================="

# Test 1: Create a static tip amount
echo "1. Creating a static tip amount..."
RESPONSE=$(curl -s -X POST "${BASE_URL}${API_PATH}" \
  -H "Content-Type: application/json" \
  -d '{"amount": 2000, "type": "static", "is_active": true}')
echo "Response: $RESPONSE"

# Extract OID from response for further tests
OID=$(echo $RESPONSE | grep -o '"oid":"[^"]*"' | cut -d'"' -f4)
echo "Created OID: $OID"
echo ""

# Test 2: Create a percentage tip amount
echo "2. Creating a percentage tip amount..."
RESPONSE2=$(curl -s -X POST "${BASE_URL}${API_PATH}" \
  -H "Content-Type: application/json" \
  -d '{"amount": 15, "type": "percentage", "is_active": true}')
echo "Response: $RESPONSE2"
echo ""

# Test 3: Get all tip amounts
echo "3. Getting all tip amounts..."
curl -s -X GET "${BASE_URL}${API_PATH}" | jq '.'
echo ""

# Test 4: Get specific tip amount by OID
if [ ! -z "$OID" ]; then
  echo "4. Getting tip amount by OID: $OID"
  curl -s -X GET "${BASE_URL}${API_PATH}/${OID}" | jq '.'
  echo ""
fi

# Test 5: Update tip amount
if [ ! -z "$OID" ]; then
  echo "5. Updating tip amount..."
  curl -s -X PUT "${BASE_URL}${API_PATH}/${OID}" \
    -H "Content-Type: application/json" \
    -d '{"amount": 2500, "type": "static", "is_active": false}' | jq '.'
  echo ""
fi

# Test 6: Try to create duplicate (should fail)
echo "6. Trying to create duplicate (should fail)..."
curl -s -X POST "${BASE_URL}${API_PATH}" \
  -H "Content-Type: application/json" \
  -d '{"amount": 2500, "type": "static", "is_active": true}' | jq '.'
echo ""

# Test 7: Try invalid data (should fail)
echo "7. Trying invalid data (should fail)..."
curl -s -X POST "${BASE_URL}${API_PATH}" \
  -H "Content-Type: application/json" \
  -d '{"amount": 150, "type": "percentage", "is_active": true}' | jq '.'
echo ""

# Test 8: Delete tip amount
if [ ! -z "$OID" ]; then
  echo "8. Deleting tip amount..."
  curl -s -X DELETE "${BASE_URL}${API_PATH}/${OID}" -w "HTTP Status: %{http_code}\n"
  echo ""
fi

# Test 9: Try to get deleted tip amount (should fail)
if [ ! -z "$OID" ]; then
  echo "9. Trying to get deleted tip amount (should fail)..."
  curl -s -X GET "${BASE_URL}${API_PATH}/${OID}" | jq '.'
  echo ""
fi

echo "Testing completed!"
echo "=================="
