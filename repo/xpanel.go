package repo

import (
	"context"
	"database/sql"
	"fmt"

	"info_service/model"
	"info_service/repo/sqls"
)

// GetAllTipAmounts retrieves all tip amounts for xpanel
func (r *Repo) GetAllTipAmounts(ctx context.Context) ([]model.TipAmount, error) {
	var tipAmounts []model.TipAmount
	err := r.dbConn.SelectContext(ctx, &tipAmounts, sqls.GetAllTipAmounts)
	return tipAmounts, err
}

// GetTipAmountByOID retrieves a specific tip amount by OID
func (r *Repo) GetTipAmountByOID(ctx context.Context, oid string) (model.TipAmount, error) {
	var tipAmount model.TipAmount
	err := r.dbConn.GetContext(ctx, &tipAmount, sqls.GetTipAmountByOID, oid)
	if err == sql.ErrNoRows {
		return tipAmount, fmt.Errorf("tip amount with OID %s not found", oid)
	}
	return tipAmount, err
}

// CreateTipAmount creates a new tip amount
func (r *Repo) CreateTipAmount(ctx context.Context, tipAmount *model.TipAmount) error {
	// Check for duplicate amount and type
	var count int
	err := r.dbConn.GetContext(ctx, &count, sqls.CheckDuplicateTipAmount, tipAmount.Amount, tipAmount.Type, "")
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("tip amount with amount %d and type %s already exists", tipAmount.Amount, tipAmount.Type)
	}

	_, err = r.dbConn.ExecContext(ctx, sqls.CreateTipAmount,
		tipAmount.OID,
		tipAmount.Amount,
		tipAmount.Type,
		tipAmount.IsActive,
		tipAmount.CreatedAt,
		tipAmount.UpdatedAt,
	)
	return err
}

// UpdateTipAmount updates an existing tip amount
func (r *Repo) UpdateTipAmount(ctx context.Context, oid string, tipAmount *model.TipAmount) error {
	// Check if tip amount exists
	var count int
	err := r.dbConn.GetContext(ctx, &count, sqls.CheckTipAmountExists, oid)
	if err != nil {
		return err
	}
	if count == 0 {
		return fmt.Errorf("tip amount with OID %s not found", oid)
	}

	// Check for duplicate amount and type (excluding current record)
	err = r.dbConn.GetContext(ctx, &count, sqls.CheckDuplicateTipAmount, tipAmount.Amount, tipAmount.Type, oid)
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("tip amount with amount %d and type %s already exists", tipAmount.Amount, tipAmount.Type)
	}

	result, err := r.dbConn.ExecContext(ctx, sqls.UpdateTipAmount,
		tipAmount.Amount,
		tipAmount.Type,
		tipAmount.IsActive,
		tipAmount.UpdatedAt,
		oid,
	)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return fmt.Errorf("tip amount with OID %s not found", oid)
	}

	return nil
}

// DeleteTipAmount deletes a tip amount by OID
func (r *Repo) DeleteTipAmount(ctx context.Context, oid string) error {
	result, err := r.dbConn.ExecContext(ctx, sqls.DeleteTipAmount, oid)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return fmt.Errorf("tip amount with OID %s not found", oid)
	}

	return nil
}

// GetTipAmountsCount gets total count of tip amounts
func (r *Repo) GetTipAmountsCount(ctx context.Context) (int, error) {
	var count int
	err := r.dbConn.GetContext(ctx, &count, sqls.GetTipAmountsCount)
	return count, err
}

// GetActiveTipAmountsByType gets active tip amounts by type for client usage
func (r *Repo) GetActiveTipAmountsByType(ctx context.Context, tipType string) ([]int, error) {
	var amounts []int
	err := r.dbConn.SelectContext(ctx, &amounts, sqls.GetActiveTipAmountsByType, tipType)
	return amounts, err
}
