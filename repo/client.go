package repo

import (
	"context"
	"info_service/model"
	"info_service/repo/sqls"
)

// func (r *Repo) GetClientNews(ctx context.Context, date, lang string, page, limit int) (resp []model.News, err error) {
// 	offset := (page - 1) * limit
// 	err = r.dbConn.SelectContext(ctx, ctx, &resp, sqls.GetClientNews, date, lang, limit, offset)
// 	return
// }

// func (r *Repo) GetNewsDetails(ctx context.Context, newsId int) (resp model.NewsDetails, err error) {
// 	err = r.dbConn.GetContext(ctx, &resp, sqls.GetNewsDetails, newsId)
// 	if err == sql.ErrNoRows {
// 		return
// 	}
// 	return
// }

func (r *Repo) GetOrderCancelReasons(ctx context.Context, lang string) (orderCancelReasons []model.GeneralResponse, err error) {
	var getOrderCancelReasons string
	switch lang {
	case "uz":
		getOrderCancelReasons = sqls.GetOrderCancelReasonsUz
	case "en":
		getOrderCancelReasons = sqls.GetOrderCancelReasonsEn
	default:
		getOrderCancelReasons = sqls.GetOrderCancelReasonsRu
	}

	err = r.dbConn.SelectContext(ctx, &orderCancelReasons, getOrderCancelReasons)
	return
}

func (r *Repo) GetClientTipAmounts(ctx context.Context) (resp []int, err error) {
	err = r.dbConn.SelectContext(ctx, &resp, sqls.GetClientTipAmounts)
	return
}

func (r *Repo) GetPercentageTipAmounts(ctx context.Context) (resp []int, err error) {
	err = r.dbConn.SelectContext(ctx, &resp, sqls.GetPercentageTipAmounts)
	return
}

func (r *Repo) GetTermsAndConditions(ctx context.Context, lang string) (data string, err error) {
	var getTermsAndConditions string
	switch lang {
	case "uz":
		getTermsAndConditions = sqls.GetTermsAndConditionsUz
	case "en":
		getTermsAndConditions = sqls.GetTermsAndConditionsEn
	default:
		getTermsAndConditions = sqls.GetTermsAndConditionsRu
	}

	err = r.dbConn.Get(&data, getTermsAndConditions)
	return
}

func (r *Repo) CreateClientAppRelease(ctx context.Context, appVersion string, platformId int8) (err error) {
	_, err = r.dbConn.ExecContext(ctx, sqls.CreateClientAppRelease, platformId, appVersion)
	return
}

func (r *Repo) GetClientAppReleaseInfo(ctx context.Context, platformId int8, version string) (resp model.AppReleaseInfo, err error) {
	err = r.dbConn.GetContext(ctx, &resp, sqls.GetClientAppReleaseInfo, version, platformId)
	return
}
