package sqls

const (
	// GetClientNews = `SELECT id, title, text_short, REPLACE(created_at, ' ', 'T') AS created_at
	// 					FROM clients_news
	// 					WHERE created_at > ? AND lang = ?
	// 					ORDER BY created_at DESC LIMIT ? OFFSET ?;`

	// GetNewsDetails = `SELECT id, title, text FROM clients_news WHERE id = ?;`

	GetOrderCancelReasonsUz = `SELECT id, name_uz as value FROM max_taxi_order_cancel_reasons WHERE is_visible=True;`
	GetOrderCancelReasonsRu = `SELECT id, name_ru as value FROM max_taxi_order_cancel_reasons WHERE is_visible=True;`
	GetOrderCancelReasonsEn = `SELECT id, name_en as value FROM max_taxi_order_cancel_reasons WHERE is_visible=True;`

	GetClientTipAmounts     = `SELECT amount FROM order_tip_amounts_xpanel WHERE type = 'static' AND is_active = true ORDER BY amount;`
	GetPercentageTipAmounts = `SELECT amount FROM order_tip_amounts_xpanel WHERE type = 'percentage' AND is_active = true ORDER BY amount;`

	GetTermsAndConditionsUz = `SELECT text_uz FROM articles WHERE alias = 'user_agreement';`
	GetTermsAndConditionsEn = `SELECT text_en FROM articles WHERE alias = 'user_agreement';`
	GetTermsAndConditionsRu = `SELECT text_ru FROM articles WHERE alias = 'user_agreement';`
)

const CreateClientAppRelease = `
INSERT IGNORE INTO max_taxi_app_releases (platform_id, title, date) VALUES(?, ?, UNIX_TIMESTAMP());`

const GetClientAppReleaseInfo = `
SELECT id, is_blocked, is_deprecated FROM max_taxi_app_releases WHERE title = ? AND platform_id = ?;`
