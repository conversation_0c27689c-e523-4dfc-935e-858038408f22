package sqls

const (
	// GetAllTipAmounts retrieves all tip amounts for xpanel
	GetAllTipAmounts = `
		SELECT oid, amount, type, is_active, created_at, updated_at 
		FROM order_tip_amounts_xpanel 
		ORDER BY created_at DESC`

	// GetTipAmountByOID retrieves a specific tip amount by OID
	GetTipAmountByOID = `
		SELECT oid, amount, type, is_active, created_at, updated_at 
		FROM order_tip_amounts_xpanel 
		WHERE oid = ?`

	// CreateTipAmount inserts a new tip amount
	CreateTipAmount = `
		INSERT INTO order_tip_amounts_xpanel (oid, amount, type, is_active, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?)`

	// UpdateTipAmount updates an existing tip amount by OID
	UpdateTipAmount = `
		UPDATE order_tip_amounts_xpanel 
		SET amount = ?, type = ?, is_active = ?, updated_at = ? 
		WHERE oid = ?`

	// DeleteTipAmount deletes a tip amount by OID
	DeleteTipAmount = `
		DELETE FROM order_tip_amounts_xpanel 
		WHERE oid = ?`

	// CheckTipAmountExists checks if a tip amount with given OID exists
	CheckTipAmountExists = `
		SELECT COUNT(*) 
		FROM order_tip_amounts_xpanel 
		WHERE oid = ?`

	// CheckDuplicateTipAmount checks if a tip amount with same amount and type already exists
	CheckDuplicateTipAmount = `
		SELECT COUNT(*) 
		FROM order_tip_amounts_xpanel 
		WHERE amount = ? AND type = ? AND oid != ?`

	// GetTipAmountsCount gets total count of tip amounts
	GetTipAmountsCount = `
		SELECT COUNT(*) 
		FROM order_tip_amounts_xpanel`

	// GetActiveTipAmountsByType gets active tip amounts by type for client usage
	GetActiveTipAmountsByType = `
		SELECT amount 
		FROM order_tip_amounts_xpanel 
		WHERE type = ? AND is_active = true 
		ORDER BY amount`
)
