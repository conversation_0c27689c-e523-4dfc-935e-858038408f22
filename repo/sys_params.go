package repo

import (
	"info_service/model"
	"info_service/repo/sqls"
	"time"
)

func (r *Repo) GetSysParam(param, defaultValue string) *model.SysParamValue {
	return r.sysParams.GetValue(param, defaultValue)
}

func (r *Repo) pollSysParams() {
	ticker := time.NewTicker(time.Second * time.Duration(r.cfg.SysParamsPollInterval))

	for {
		r.log.Info("get system params...")
		err := r.getSysParams()
		if err != nil {
			r.log.Error("get system params: ", err)
		}
		<-ticker.C
	}
}

func (r *Repo) getSysParams() (err error) {
	var sysParamsFromDb []model.SysParam

	err = r.dbConn.Select(&sysParamsFromDb, sqls.GetSysParams)
	if err != nil {
		return
	}

	r.sysParams.Load(sysParamsFromDb)

	return
}
