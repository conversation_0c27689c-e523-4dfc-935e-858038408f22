package repo

import (
	"bytes"
	"context"
	"errors"
	"net/http"
)

func (r *Repo) DoRequest(ctx context.Context, method, url string, request, response interface{}) (err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = errors.New("http status not ok: " + resp.Status)
		return
	}

	return json.NewDecoder(resp.Body).Decode(&response)
}

func (r *Repo) DoRequestWithoutBody(ctx context.Context, method, url string, response interface{}) (err error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = errors.New("http status not ok: " + resp.Status)
		return
	}

	return json.NewDecoder(resp.Body).Decode(&response)
}

func (r *Repo) DoRequestWithoutResponse(ctx context.Context, method, url string, request interface{}) (err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = errors.New("http status not ok: " + resp.Status)
		return
	}

	return
}
