package repo

import (
	"context"
	"info_service/model"
	"info_service/repo/sqls"
)

// func (r *Repo) DriverSupportContactGet(driverId int) (contact model.DriverSupportContact, err error) {
// 	err = r.dbConn.Get(&contact, sqls.DriverSupportContactGet, driverId)
// 	if err == sql.ErrNoRows {
// 		err = nil
// 		return
// 	}
// 	return
// }

func (r *Repo) CreateDriverAppRelease(ctx context.Context, appVersion string) (err error) {
	_, err = r.dbConn.ExecContext(ctx, sqls.CreateDriverAppRelease, appVersion)
	return
}

func (r *Repo) GetDriverAppReleaseInfo(ctx context.Context, version string) (resp model.AppReleaseInfo, err error) {
	err = r.dbConn.GetContext(ctx, &resp, sqls.GetDriverAppReleaseInfo, version)
	return
}
