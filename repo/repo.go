package repo

import (
	"context"
	"net/http"
	"sync"
	"time"

	"info_service/model"
	"info_service/util/config"
	"info_service/util/logger"

	"github.com/bytedance/sonic"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
)

const RequestTimeout = 10 * time.Second

var (
	json = sonic.ConfigDefault
	once sync.Once
	repo *Repo
)

type Repo struct {
	cfg        *config.Config
	log        *logger.Logger
	dbConn     *sqlx.DB
	ctx        context.Context
	httpClient *http.Client
	sysParams  *model.SysParams
}

func Get() *Repo {
	once.Do(func() {
		cfg := config.Get()

		db := sqlx.MustConnect("mysql", cfg.Mysql.GetDSN())

		db.SetConnMaxLifetime(60 * time.Minute)
		db.SetMaxIdleConns(10)
		db.SetMaxOpenConns(10)

		err := db.Ping()
		if err != nil {
			panic(err)
		}

		repo = &Repo{
			cfg:        cfg,
			log:        logger.Get(cfg.LogLevel),
			dbConn:     db,
			ctx:        context.Background(),
			httpClient: &http.Client{Timeout: RequestTimeout},
			sysParams:  model.NewSysParams(),
		}

		// polling system params
		go repo.pollSysParams()
	})

	return repo
}
