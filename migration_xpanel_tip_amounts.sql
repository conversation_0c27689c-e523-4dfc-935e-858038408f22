-- Migration script to create xpanel tip amounts table
-- This table will store tip amounts with OID support for xpanel management

CREATE TABLE IF NOT EXISTS `order_tip_amounts_xpanel` (
  `oid` VARCHAR(36) NOT NULL PRIMARY KEY COMMENT 'Unique identifier (UUID)',
  `amount` INT NOT NULL COMMENT 'Tip amount value',
  `type` ENUM('static', 'percentage') NOT NULL COMMENT 'Type of tip amount',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this tip amount is active',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
  
  -- Indexes for better performance
  INDEX `idx_type_active` (`type`, `is_active`),
  INDEX `idx_amount` (`amount`),
  INDEX `idx_created_at` (`created_at`),
  
  -- Unique constraint to prevent duplicate amount+type combinations
  UNIQUE KEY `uk_amount_type` (`amount`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tip amounts configuration for xpanel management';

-- Insert some default tip amounts for testing
INSERT INTO `order_tip_amounts_xpanel` (`oid`, `amount`, `type`, `is_active`) VALUES
(UUID(), 1000, 'static', TRUE),
(UUID(), 2000, 'static', TRUE),
(UUID(), 3000, 'static', TRUE),
(UUID(), 5000, 'static', TRUE),
(UUID(), 10000, 'static', TRUE),
(UUID(), 5, 'percentage', TRUE),
(UUID(), 10, 'percentage', TRUE),
(UUID(), 15, 'percentage', TRUE),
(UUID(), 20, 'percentage', TRUE)
ON DUPLICATE KEY UPDATE 
  `updated_at` = CURRENT_TIMESTAMP;

-- Optional: Migrate existing data from order_tip_amounts table if needed
-- INSERT INTO `order_tip_amounts_xpanel` (`oid`, `amount`, `type`, `is_active`)
-- SELECT UUID(), `amount`, `type`, TRUE
-- FROM `order_tip_amounts`
-- WHERE `amount` IS NOT NULL AND `type` IS NOT NULL
-- ON DUPLICATE KEY UPDATE 
--   `updated_at` = CURRENT_TIMESTAMP;
