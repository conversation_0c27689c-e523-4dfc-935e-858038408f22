package config

import (
	"fmt"
	"sync"

	"github.com/ilyakaznacheev/cleanenv"
)

var (
	cfg  *Config
	once sync.Once
)

type Mysql struct {
	Host string `env:"M_DB_HOST" env-required:"true"`
	Name string `env:"M_DB_NAME" env-required:"true"`
	User string `env:"M_DB_USER" env-required:"true"`
	Pass string `env:"M_DB_PASS" env-required:"true"`
	Port string `env:"M_DB_PORT" env-required:"true"`
}

// GetDSN  need to make dsn connection string
func (db *Mysql) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?parseTime=true&loc=%s", db.User, db.Pass, db.Host, db.Port, db.Name, "Asia%2FTashkent")
}

type Config struct {
	Host string `env:"HOST" env-required:"true"`

	Mysql Mysql

	SysParamsPollInterval     int    `env:"SYS_PARAMS_POLL_INTERVAL" env-default:"300"`
	ClientAndroidAppUpdateUrl string `env:"CLIENT_ANDROID_APP_UPDATE_URL"`
	ClientIosAppUpdateUrl     string `env:"CLIENT_IOS_APP_UPDATE_URL"`
	DriverAndroidAppUpdateUrl string `env:"DRIVER_ANDROID_APP_UPDATE_URL"`

	Redis struct {
		Host     string `env:"REDIS_HOST"`
		Password string `env:"REDIS_PASSWORD"`
		DB       int    `env:"REDIS_DB"`
	}

	LogLevel          string `env:"LOG_LEVEL" env-default:"300"`
	RequestLogEnabled bool   `env:"REQUEST_LOG_ENABLED" env-default:"true"`
}

func Get() *Config {
	once.Do(func() {
		cfg = new(Config)
		err := cleanenv.ReadEnv(cfg)
		if err != nil {
			panic(err)
		}
	})
	return cfg
}
