package util

import (
	"github.com/hashicorp/go-version"
)

// CompareVersion:
// if version1 > version2 return 1
// if version1 < version2 return -1
// if version1 == version2 return 0
func CompareVersion(version1, version2 string) int {
	v1, err := version.NewVersion(version1)
	if err != nil {
		return 1
	}
	v2, err := version.NewVersion(version2)
	if err != nil {
		return 1
	}
	return v1.Compare(v2)
}

func GetLang(lang string) string {
	switch lang {
	case "ru", "uz", "en":
		return lang
	default:
		return "ru"
	}
}
