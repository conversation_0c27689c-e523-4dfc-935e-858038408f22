-- Migration script to add OID support to existing order_tip_amounts table
-- This will modify the existing table to support xpanel management with OID

-- Step 1: Add new columns to existing order_tip_amounts table
ALTER TABLE `order_tip_amounts` 
ADD COLUMN `oid` VARCHAR(36) NULL COMMENT 'Unique identifier (UUID)' AFTER `id`,
ADD COLUMN `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this tip amount is active' AFTER `type`,
ADD COLUMN `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp' AFTER `is_active`,
ADD COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp' AFTER `created_at`;

-- Step 2: Generate OIDs for existing records
UPDATE `order_tip_amounts` SET `oid` = UUID() WHERE `oid` IS NULL;

-- Step 3: Make OID column NOT NULL and add unique constraint
ALTER TABLE `order_tip_amounts` 
MODIFY COLUMN `oid` VARCHAR(36) NOT NULL,
ADD UNIQUE KEY `uk_oid` (`oid`);

-- Step 4: Add indexes for better performance
ALTER TABLE `order_tip_amounts`
ADD INDEX `idx_type_active` (`type`, `is_active`),
ADD INDEX `idx_amount` (`amount`),
ADD INDEX `idx_created_at` (`created_at`);

-- Step 5: Add unique constraint to prevent duplicate amount+type combinations (if not exists)
-- Note: This might fail if there are existing duplicates, handle manually if needed
-- ALTER TABLE `order_tip_amounts`
-- ADD UNIQUE KEY `uk_amount_type` (`amount`, `type`);

-- Step 6: Update table comment
ALTER TABLE `order_tip_amounts` COMMENT='Tip amounts configuration with xpanel OID support';

-- Step 7: Show the updated table structure
-- DESCRIBE `order_tip_amounts`;
