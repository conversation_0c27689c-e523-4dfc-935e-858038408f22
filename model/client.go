package model

type GeneralResponse struct {
	Id    int    `json:"id"`
	Value string `json:"value"`
}

// type News struct {
// 	Id      int    `json:"id" db:"id"`
// 	Title   string `json:"title" db:"title"`
// 	Preview string `json:"preview" db:"text_short"`
// 	Date    string `json:"date" db:"created_at"`
// }

// type NewsDetails struct {
// 	Id    int    `json:"id"`
// 	Title string `json:"title"`
// 	Text  string `json:"text"`
// }
