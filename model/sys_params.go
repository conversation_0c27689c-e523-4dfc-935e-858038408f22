package model

import (
	"strconv"
	"sync"
)

const maxSysParams = 1000

type SysParamValue string

type (
	SysParam struct {
		Param string        `db:"param"`
		Value SysParamValue `db:"value"`
	}

	SysParams struct {
		sync.RWMutex
		cache map[string]SysParamValue
	}
)

func NewSysParams() *SysParams {
	return &SysParams{
		cache: make(map[string]SysParamValue, maxSysParams),
	}
}

func (c *SysParams) Load(sysParams []SysParam) {
	c.Lock()
	defer c.Unlock()

	c.cache = make(map[string]SysParamValue, len(sysParams))

	for _, v := range sysParams {
		c.cache[v.Param] = v.Value
	}
}

func (c *SysParams) GetValue(param, defaultValue string) *SysParamValue {
	c.RLock()
	defer c.RUnlock()

	value, ok := c.cache[param]
	if !ok {
		value.Set(defaultValue)
	}
	return &value
}

func (v *SysParamValue) Set(value string) {
	*v = SysParamValue(value)
}

func (v SysParamValue) Str() string {
	return string(v)
}

func (v SysParamValue) Int64() int64 {
	value, _ := strconv.ParseInt(string(v), 10, 64)
	return value
}

func (v SysParamValue) Int() int {
	value, _ := strconv.Atoi(string(v))
	return value
}

func (v SysParamValue) Bool() bool {
	value, _ := strconv.ParseBool(string(v))
	return value
}

func (v SysParamValue) Float64() float64 {
	value, _ := strconv.ParseFloat(string(v), 64)
	return value
}
