package model

import (
	"time"

	"github.com/google/uuid"
)

// TipAmount represents a tip amount configuration
type TipAmount struct {
	OID       string    `json:"oid" db:"oid"`
	Amount    int       `json:"amount" db:"amount"`
	Type      string    `json:"type" db:"type"` // "static" or "percentage"
	IsActive  bool      `json:"is_active" db:"is_active"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// TipAmountRequest represents the request payload for creating/updating tip amounts
type TipAmountRequest struct {
	Amount   int    `json:"amount" validate:"required,min=1"`
	Type     string `json:"type" validate:"required,oneof=static percentage"`
	IsActive *bool  `json:"is_active"`
}

// TipAmountResponse represents the response structure for tip amounts
type TipAmountResponse struct {
	OID       string    `json:"oid"`
	Amount    int       `json:"amount"`
	Type      string    `json:"type"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TipAmountListResponse represents the response for listing tip amounts
type TipAmountListResponse struct {
	TipAmounts []TipAmountResponse `json:"tip_amounts"`
	Total      int                 `json:"total"`
}

// NewTipAmount creates a new TipAmount with generated OID
func NewTipAmount(amount int, tipType string, isActive bool) *TipAmount {
	now := time.Now()
	return &TipAmount{
		OID:       uuid.New().String(),
		Amount:    amount,
		Type:      tipType,
		IsActive:  isActive,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// ToResponse converts TipAmount to TipAmountResponse
func (t *TipAmount) ToResponse() TipAmountResponse {
	return TipAmountResponse{
		OID:       t.OID,
		Amount:    t.Amount,
		Type:      t.Type,
		IsActive:  t.IsActive,
		CreatedAt: t.CreatedAt,
		UpdatedAt: t.UpdatedAt,
	}
}

// Update updates the tip amount with new values
func (t *TipAmount) Update(amount int, tipType string, isActive bool) {
	t.Amount = amount
	t.Type = tipType
	t.IsActive = isActive
	t.UpdatedAt = time.Now()
}
